<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<view class="login-content">
			<!-- 头部logo区域 -->
			<view class="login-header">
				<u-avatar
					:src="'/static/logo.png'"
					size="120"
					shape="circle"
					class="logo-avatar"
				></u-avatar>
				<u-text
					:text="appName"
					type="primary"
					size="24"
					bold
					class="app-name"
				></u-text>
				<u-text
					text="欢迎回来，请登录您的账户"
					type="tips"
					size="14"
					class="welcome-text"
				></u-text>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<u-form
					:model="form"
					:rules="rules"
					ref="loginForm"
					label-position="top"
				>
					<!-- 用户名输入框 -->
					<view class="form-item">
						<view class="input-container">
							<view class="input-label">
								<u-icon name="account-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">用户名/手机号</text>
							</view>
							<view class="input-box">
								<input
									v-model="form.username"
									placeholder="请输入用户名或手机号"
									class="native-input"
									type="text"
								/>
								<u-icon
									v-if="form.username"
									name="close-circle-fill"
									color="#ccc"
									size="16"
									class="clear-icon"
									@click="form.username = ''"
								></u-icon>
							</view>
						</view>
					</view>

					<!-- 密码输入框 -->
					<view class="form-item">
						<view class="input-container">
							<view class="input-label">
								<u-icon name="lock-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">密码</text>
							</view>
							<view class="input-box">
								<input
									v-model="form.password"
									:type="passwordVisible ? 'text' : 'password'"
									placeholder="请输入密码"
									class="native-input"
								/>
								<u-icon
									v-if="form.password"
									:name="passwordVisible ? 'eye-fill' : 'eye-off'"
									color="#ccc"
									size="16"
									class="password-icon"
									@click="togglePasswordVisible"
								></u-icon>
							</view>
						</view>
					</view>

					<view class="login-options">
						<u-checkbox
							v-model="rememberPassword"
							active-color="#5677fc"
							size="18"
						>记住密码</u-checkbox>

						<u-text
							text="忘记密码？"
							type="primary"
							@click="forgotPassword"
							class="forgot-link"
						></u-text>
					</view>

					<u-button
						type="primary"
						:loading="loginLoading"
						@click="handleLogin"
						shape="circle"
						size="large"
						class="login-btn"
						:custom-style="loginBtnStyle"
					>
						{{ loginLoading ? '登录中...' : '登录' }}
					</u-button>
				</u-form>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<u-divider
					text="其他登录方式"
					text-color="#999"
					line-color="#e4e7ed"
					margin-top="40"
					margin-bottom="30"
				></u-divider>

				<view class="third-party-buttons">
					<!-- #ifdef MP-WEIXIN -->
					<u-button
						type="success"
						@click="wechatLogin"
						shape="circle"
						icon="weixin-fill"
						size="normal"
						class="third-btn"
						:custom-style="wechatBtnStyle"
					>
						微信登录
					</u-button>
					<!-- #endif -->

					<!-- #ifdef APP-PLUS -->
					<u-button
						type="info"
						@click="qqLogin"
						shape="circle"
						icon="qq-fill"
						size="normal"
						class="third-btn"
						:custom-style="qqBtnStyle"
					>
						QQ登录
					</u-button>
					<!-- #endif -->

					<!-- H5端显示更多登录方式 -->
					<!-- #ifdef H5 -->
					<view class="social-login-grid">
						<u-button
							type="success"
							@click="wechatLogin"
							shape="circle"
							icon="weixin-fill"
							size="normal"
							class="social-btn"
							:custom-style="wechatBtnStyle"
						>
							微信
						</u-button>

						<u-button
							type="info"
							@click="qqLogin"
							shape="circle"
							icon="qq-fill"
							size="normal"
							class="social-btn"
							:custom-style="qqBtnStyle"
						>
							QQ
						</u-button>
					</view>
					<!-- #endif -->
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-section">
				<u-text
					text="还没有账号？"
					color="#999"
					size="14"
				></u-text>
				<u-text
					text="立即注册"
					type="primary"
					@click="goRegister"
					size="14"
					class="register-link"
				></u-text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp模板',
			loginLoading: false,
			rememberPassword: false,
			passwordVisible: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: 'blur'
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: 'blur'
					}
				]
			},

			// uView按钮样式
			loginBtnStyle: {
				background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				border: 'none',
				height: '50px',
				fontSize: '16px'
			},
			wechatBtnStyle: {
				background: '#07c160',
				border: 'none'
			},
			qqBtnStyle: {
				background: '#12b7f5',
				border: 'none'
			}
		}
	},
	
	onLoad() {
		this.loadRememberedPassword()
	},
	
	methods: {
		// 处理登录
		async handleLogin() {
			// 使用uView表单验证
			try {
				await this.$refs.loginForm.validate()
			} catch (errors) {
				this.$u.toast('请检查表单信息')
				return
			}

			this.loginLoading = true

			try {
				// 模拟登录API调用
				console.log('登录信息:', {
					username: this.form.username,
					password: this.form.password
				})

				// 模拟网络延迟
				await new Promise(resolve => setTimeout(resolve, 1000))

				// 模拟登录成功
				this.$u.toast('登录成功')

				// 保存用户信息
				this.saveUserInfo({
					token: 'mock-token-' + Date.now(),
					userInfo: {
						username: this.form.username,
						avatar: '',
						phone: ''
					}
				})

				// 记住密码
				if (this.rememberPassword) {
					this.savePassword()
				} else {
					this.clearPassword()
				}

				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1500)
			} catch (error) {
				console.error('登录失败:', error)
				this.$u.toast('登录失败，请重试')
			} finally {
				this.loginLoading = false
			}
		},
		
		// 微信登录
		wechatLogin() {
			this.$u.toast('微信登录功能待开发')
		},

		// QQ登录
		qqLogin() {
			this.$u.toast('QQ登录功能待开发')
		},

		// 切换密码可见性
		togglePasswordVisible() {
			this.passwordVisible = !this.passwordVisible
		},
		
		// 保存用户信息
		async saveUserInfo(userData) {
			// 保存到本地存储
			uni.setStorageSync('token', userData.token)
			uni.setStorageSync('userInfo', userData.userInfo)
			
			// 保存到Vuex
			this.$store.commit('user/setToken', userData.token)
			this.$store.commit('user/setUserInfo', userData.userInfo)
		},
		
		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', {
				username: this.form.username,
				password: this.form.password
			})
		},
		
		// 清除保存的密码
		clearPassword() {
			uni.removeStorageSync('rememberedPassword')
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('rememberedPassword')
			if (remembered) {
				this.form.username = remembered.username
				this.form.password = remembered.password
				this.rememberPassword = true
			}
		},
		
		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},
		
		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
}

.circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float 6s ease-in-out infinite;
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: 10%;
	right: 10%;
	animation-delay: 0s;
}

.circle-2 {
	width: 150rpx;
	height: 150rpx;
	bottom: 20%;
	left: 15%;
	animation-delay: 2s;
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	top: 60%;
	right: 20%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.7;
	}
	50% {
		transform: translateY(-20px) rotate(180deg);
		opacity: 1;
	}
}

.login-content {
	position: relative;
	z-index: 1;
	padding: 100rpx 60rpx 60rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-avatar {
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}

.app-name {
	margin-bottom: 20rpx;
	color: #fff !important;
}

.welcome-text {
	color: rgba(255, 255, 255, 0.8) !important;
}

.login-form {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
}

.form-item {
	margin-bottom: 30rpx;
}

/* 自定义输入框样式 */
.input-container {
	width: 100%;
	margin-bottom: 20rpx;
}

.input-label {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
	gap: 10rpx;
}

.label-text {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.input-box {
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e9ecef;
	padding: 0 16rpx;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	position: relative;
	min-height: 96rpx;
}

.input-box:focus-within {
	border-color: #667eea;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	background: #fff;
}

.native-input {
	flex: 1;
	background: transparent;
	border: none;
	outline: none;
	font-size: 32rpx;
	color: #333;
	padding: 24rpx 0;
}

.native-input::placeholder {
	color: #999;
	font-size: 30rpx;
}

.clear-icon,
.password-icon {
	margin-left: 16rpx;
	cursor: pointer;
	flex-shrink: 0;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx 0 40rpx;
}

.forgot-link {
	cursor: pointer;
}

.login-btn {
	margin-top: 20rpx;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.third-party-login {
	margin-top: 40rpx;
}

.third-party-buttons {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.third-btn {
	flex: 1;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.social-login-grid {
	display: flex;
	gap: 20rpx;
}

.social-btn {
	flex: 1;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.register-section {
	text-align: center;
	margin-top: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10rpx;
}

.register-link {
	cursor: pointer;
	text-decoration: underline;
}

/* uView组件样式覆盖 */
::v-deep .u-checkbox__label {
	font-size: 28rpx !important;
	color: #666 !important;
}

/* 输入框内部样式覆盖 */
::v-deep .u-input__content {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
}

::v-deep .u-input__content__field-wrapper {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field {
	background: transparent !important;
	color: #333 !important;
	font-size: 32rpx !important;
	border: none !important;
	padding: 12rpx 0 !important;
}

::v-deep .u-input__content__clear-icon {
	color: #999 !important;
}

::v-deep .u-input__content__password-icon {
	color: #999 !important;
}
</style>
