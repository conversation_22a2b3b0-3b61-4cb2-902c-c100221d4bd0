<template>
	<view class="login-container">
		<view class="login-header">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="app-name">{{ appName }}</text>
		</view>

		<view class="login-form">
			<view class="form-item">
				<input
					v-model="form.username"
					placeholder="请输入用户名/手机号"
					class="input"
				/>
			</view>

			<view class="form-item">
				<input
					v-model="form.password"
					type="password"
					placeholder="请输入密码"
					class="input"
				/>
			</view>

			<view class="login-options">
				<checkbox v-model="rememberPassword">记住密码</checkbox>
				<text class="forgot-password" @click="forgotPassword">忘记密码？</text>
			</view>

			<button
				type="primary"
				:loading="loginLoading"
				@click="handleLogin"
				class="login-btn"
			>
				登录
			</button>
		</view>
		
		<view class="third-party-login">
			<view class="divider">
				<text>其他登录方式</text>
			</view>

			<view class="third-party-buttons">
				<!-- #ifdef MP-WEIXIN -->
				<button
					type="default"
					@click="wechatLogin"
					class="third-btn wechat-btn"
				>
					微信登录
				</button>
				<!-- #endif -->

				<!-- #ifdef APP-PLUS -->
				<button
					type="default"
					@click="qqLogin"
					class="third-btn qq-btn"
				>
					QQ登录
				</button>
				<!-- #endif -->
			</view>
		</view>
		
		<view class="register-link">
			<text>还没有账号？</text>
			<text class="link-text" @click="goRegister">立即注册</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp模板',
			loginLoading: false,
			rememberPassword: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: 'blur'
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: 'blur'
					}
				]
			}
		}
	},
	
	onLoad() {
		this.loadRememberedPassword()
	},
	
	methods: {
		// 处理登录
		async handleLogin() {
			// 简单验证
			if (!this.form.username) {
				uni.showToast({
					title: '请输入用户名',
					icon: 'none'
				})
				return
			}

			if (!this.form.password) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				})
				return
			}

			this.loginLoading = true

			try {
				const loginData = {
					username: this.form.username,
					password: this.form.password
				}

				// 模拟登录成功
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				})

				// 保存用户信息
				this.saveUserInfo({
					token: 'mock-token-' + Date.now(),
					userInfo: {
						username: this.form.username,
						avatar: '',
						phone: ''
					}
				})

				// 记住密码
				if (this.rememberPassword) {
					this.savePassword()
				} else {
					this.clearPassword()
				}

				// 跳转到首页
				setTimeout(() => {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}, 1500)
			} catch (error) {
				console.error('登录失败:', error)
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				})
			} finally {
				this.loginLoading = false
			}
		},
		
		// 微信登录
		wechatLogin() {
			uni.showToast({
				title: '微信登录功能待开发',
				icon: 'none'
			})
		},

		// QQ登录
		qqLogin() {
			uni.showToast({
				title: 'QQ登录功能待开发',
				icon: 'none'
			})
		},
		
		// 保存用户信息
		async saveUserInfo(userData) {
			// 保存到本地存储
			uni.setStorageSync('token', userData.token)
			uni.setStorageSync('userInfo', userData.userInfo)
			
			// 保存到Vuex
			this.$store.commit('user/setToken', userData.token)
			this.$store.commit('user/setUserInfo', userData.userInfo)
		},
		
		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', {
				username: this.form.username,
				password: this.form.password
			})
		},
		
		// 清除保存的密码
		clearPassword() {
			uni.removeStorageSync('rememberedPassword')
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('rememberedPassword')
			if (remembered) {
				this.form.username = remembered.username
				this.form.password = remembered.password
				this.rememberPassword = true
			}
		},
		
		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},
		
		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 100rpx 60rpx 60rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 100rpx;
}

.logo {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
}

.login-form {
	background: #fff;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
	margin-bottom: 40rpx;
}

.input {
	width: 100%;
	height: 88rpx;
	padding: 0 20rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 10rpx;
	font-size: 32rpx;
	box-sizing: border-box;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40rpx 0;
}

.forgot-password {
	color: #3c9cff;
	font-size: 28rpx;
}

.login-btn {
	width: 100%;
	margin-top: 40rpx;
	height: 88rpx;
	border-radius: 44rpx;
	background: #3c9cff;
	color: #fff;
	font-size: 32rpx;
	border: none;
}

.third-party-login {
	margin-top: 60rpx;
}

.divider {
	text-align: center;
	position: relative;
	margin-bottom: 40rpx;
}

.divider text {
	background: transparent;
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
	padding: 0 20rpx;
}

.divider::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	height: 1rpx;
	background: rgba(255, 255, 255, 0.3);
	z-index: -1;
}

.third-party-buttons {
	display: flex;
	gap: 20rpx;
}

.third-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.wechat-btn {
	background: #07c160;
	color: #fff;
}

.qq-btn {
	background: #12b7f5;
	color: #fff;
}

.register-link {
	text-align: center;
	margin-top: 60rpx;
}

.register-link text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
}

.link-text {
	color: #fff;
	font-weight: bold;
	margin-left: 10rpx;
}
</style>
