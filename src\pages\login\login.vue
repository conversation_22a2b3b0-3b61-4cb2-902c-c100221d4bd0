<template>
	<view class="login-container">
		<view class="login-content">
			<!-- 头部logo区域 -->
			<view class="login-header">
				<image src="/static/logo.png" class="logo-image" mode="aspectFit"></image>
				<text class="app-name">{{ appName }}</text>
				<text class="welcome-text">欢迎回来，请登录您的账户</text>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<!-- 用户名输入框 -->
				<view class="input-group">
					<view class="input-label">
						<u-icon name="account-fill" color="#667eea" size="18"></u-icon>
						<text class="label-text">用户名/手机号</text>
					</view>
					<u-input
						v-model="form.username"
						placeholder="请输入用户名或手机号"
						border="none"
						clearable
						class="custom-input"
					></u-input>
				</view>

				<!-- 密码输入框 -->
				<view class="input-group">
					<view class="input-label">
						<u-icon name="lock-fill" color="#667eea" size="18"></u-icon>
						<text class="label-text">密码</text>
					</view>
					<u-input
						v-model="form.password"
						type="password"
						placeholder="请输入密码"
						border="none"
						password
						class="custom-input"
					></u-input>
				</view>

				<!-- 登录选项 -->
				<view class="login-options">
					<view class="remember-password" @click="toggleRememberPassword">
						<view class="custom-checkbox" :class="{ 'checked': rememberPassword }">
							<u-icon v-if="rememberPassword" name="checkmark" color="#fff" size="12"></u-icon>
						</view>
						<text class="checkbox-label">记住密码</text>
					</view>
					<text class="forgot-link" @click="forgotPassword">忘记密码？</text>
				</view>

				<!-- 登录按钮 -->
				<u-button
					type="primary"
					size="large"
					@click="handleLogin"
					:loading="loginLoading"
					:custom-style="loginButtonStyle"
					class="login-button"
				>
					{{ loginLoading ? '登录中...' : '登录' }}
				</u-button>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<u-divider text="其他登录方式" margin-top="40" margin-bottom="30"></u-divider>
				<view class="social-buttons">
					<u-button type="success" @click="wechatLogin" class="social-btn">微信登录</u-button>
					<u-button type="info" @click="qqLogin" class="social-btn">QQ登录</u-button>
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-section">
				<text class="register-text">还没有账号？</text>
				<text class="register-link" @click="goRegister">立即注册</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp Demo',
			loginLoading: false,
			rememberPassword: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: ['blur', 'change']
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: ['blur', 'change']
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: ['blur', 'change']
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: ['blur', 'change']
					}
				]
			},
			loginButtonStyle: {
				width: '100%',
				height: '88rpx',
				borderRadius: '44rpx',
				background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				marginTop: '60rpx'
			}
		}
	},
	methods: {
		// 切换记住密码
		toggleRememberPassword() {
			this.rememberPassword = !this.rememberPassword
		},

		// 处理登录
		async handleLogin() {
			// 表单验证
			const valid = await this.$refs.loginForm.validate()
			if (!valid) {
				this.$u.toast('请检查表单信息')
				return
			}

			this.loginLoading = true
			
			try {
				// 模拟登录API调用
				await this.mockLoginAPI()
				
				// 登录成功处理
				this.$u.toast('登录成功')
				
				// 保存用户信息
				if (this.rememberPassword) {
					this.savePassword()
				}
				
				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1500)
				
			} catch (error) {
				this.$u.toast(error.message || '登录失败')
			} finally {
				this.loginLoading = false
			}
		},

		// 模拟登录API
		mockLoginAPI() {
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					if (this.form.username === 'admin' && this.form.password === '123456') {
						resolve({
							token: 'mock-token-123456',
							userInfo: {
								id: 1,
								username: this.form.username,
								nickname: '管理员'
							}
						})
					} else {
						reject(new Error('用户名或密码错误'))
					}
				}, 2000)
			})
		},

		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', this.form.password)
		},

		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},

		// 微信登录
		wechatLogin() {
			this.$u.toast('微信登录功能待开发')
		},

		// QQ登录
		qqLogin() {
			this.$u.toast('QQ登录功能待开发')
		},

		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 80rpx 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.login-content {
	width: 100%;
	max-width: 600rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 80rpx;

	.logo-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 20rpx;
		margin-bottom: 40rpx;
	}

	.app-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 20rpx;
	}

	.welcome-text {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
}

.login-form {
	background: #fff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.input-group {
	margin-bottom: 40rpx;

	.input-label {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.label-text {
			margin-left: 16rpx;
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40rpx 0 60rpx 0;
}

.remember-password {
	display: flex;
	align-items: center;

	.custom-checkbox {
		width: 32rpx;
		height: 32rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
		transition: all 0.3s;

		&.checked {
			background: #667eea;
			border-color: #667eea;
		}
	}

	.checkbox-label {
		font-size: 28rpx;
		color: #666;
	}
}

.forgot-link {
	font-size: 28rpx;
	color: #667eea;
}

.third-party-login {
	margin: 40rpx 0;

	.social-buttons {
		display: flex;
		gap: 20rpx;

		.social-btn {
			flex: 1;
		}
	}
}

.register-section {
	text-align: center;
	margin-top: 40rpx;

	.register-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.register-link {
		margin-left: 10rpx;
		font-size: 28rpx;
		color: #fff;
		text-decoration: underline;
	}
}

/* 自定义输入框样式 */
.custom-input {
	::v-deep .u-input__content {
		background-color: #f8f9fa !important;
		border-radius: 12rpx !important;
		padding: 0 20rpx !important;
		height: 88rpx !important;
		min-height: 88rpx !important;
		max-height: 88rpx !important;
		border: 2rpx solid transparent !important;
		transition: all 0.3s ease !important;
		box-sizing: border-box !important;
		overflow: hidden !important;
	}

	::v-deep .u-input--focus .u-input__content {
		border-color: #667eea !important;
		background-color: #fff !important;
	}

	::v-deep .u-input__content__field-wrapper {
		height: 100% !important;
		display: flex !important;
		align-items: center !important;
		overflow: hidden !important;
	}

	::v-deep .u-input__content__field-wrapper__field {
		font-size: 30rpx !important;
		line-height: 1.4 !important;
		flex: 1 !important;
	}

	/* 密码显示/隐藏按钮样式 */
	::v-deep .u-input__content__password-icon {
		width: 40rpx !important;
		height: 40rpx !important;
		margin-left: 10rpx !important;
		flex-shrink: 0 !important;
	}

	/* 清除按钮样式 */
	::v-deep .u-input__content__clear-icon {
		width: 40rpx !important;
		height: 40rpx !important;
		margin-left: 10rpx !important;
		flex-shrink: 0 !important;
	}
}
</style>
