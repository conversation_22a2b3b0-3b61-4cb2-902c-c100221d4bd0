<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<view class="login-content">
			<!-- 头部logo区域 -->
			<view class="login-header">
				<view class="logo-container">
					<image
						src="/static/logo.png"
						class="logo-image"
						mode="aspectFit"
					></image>
				</view>
				<view class="app-title">
					<text class="app-name">{{ appName }}</text>
					<text class="welcome-text">欢迎回来，请登录您的账户</text>
				</view>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<up-form
					:model="form"
					:rules="rules"
					ref="loginForm"
					label-position="top"
					:error-type="['message']"
				>
					<!-- 用户名输入框 -->
					<up-form-item
						prop="username"
						label=""
						class="form-item"
					>
						<view class="input-container">
							<view class="input-label">
								<u-icon name="account-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">用户名/手机号</text>
							</view>
							<up-input
								v-model="form.username"
								placeholder="请输入用户名或手机号"
								border="none"
								clearable
								height="48"
								:custom-style="inputStyle"
								class="uview-input"
							></up-input>
						</view>
					</up-form-item>

					<!-- 密码输入框 -->
					<up-form-item
						prop="password"
						label=""
						class="form-item"
					>
						<view class="input-container">
							<view class="input-label">
								<u-icon name="lock-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">密码</text>
							</view>
							<up-input
								v-model="form.password"
								type="password"
								placeholder="请输入密码"
								border="none"
								clearable
								password
								height="48"
								:custom-style="inputStyle"
								class="uview-input"
							></up-input>
						</view>
					</up-form-item>

					<view class="login-options">
						<view class="remember-password" @click="toggleRememberPassword">
							<view class="checkbox-container">
								<view class="custom-checkbox" :class="{ 'checked': rememberPassword }">
									<u-icon v-if="rememberPassword" name="checkmark" color="#fff" size="12"></u-icon>
								</view>
								<text class="checkbox-label">记住密码</text>
							</view>
						</view>

						<text class="forgot-link" @click="forgotPassword">忘记密码？</text>
					</view>

					<view class="login-button-container">
						<view
							class="login-button"
							:class="{ 'loading': loginLoading }"
							@click="handleLogin"
						>
							<u-icon v-if="loginLoading" name="loading" color="#fff" size="18" class="loading-icon"></u-icon>
							<text class="login-button-text">{{ loginLoading ? '登录中...' : '登录' }}</text>
						</view>
					</view>
				</up-form>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<up-divider
					text="其他登录方式"
					text-color="#999"
					line-color="#e4e7ed"
					margin-top="40"
					margin-bottom="30"
				></up-divider>

				<view class="third-party-buttons">
					<!-- #ifdef MP-WEIXIN -->
					<up-button
						type="success"
						@click="wechatLogin"
						shape="circle"
						icon="weixin-fill"
						size="normal"
						class="third-btn"
						:custom-style="wechatBtnStyle"
					>
						微信登录
					</up-button>
					<!-- #endif -->

					<!-- #ifdef APP-PLUS -->
					<up-button
						type="info"
						@click="qqLogin"
						shape="circle"
						icon="qq-fill"
						size="normal"
						class="third-btn"
						:custom-style="qqBtnStyle"
					>
						QQ登录
					</up-button>
					<!-- #endif -->

					<!-- H5端显示更多登录方式 -->
					<!-- #ifdef H5 -->
					<view class="social-login-grid">
						<up-button
							type="success"
							@click="wechatLogin"
							shape="circle"
							icon="weixin-fill"
							size="normal"
							class="social-btn"
							:custom-style="wechatBtnStyle"
						>
							微信
						</up-button>

						<up-button
							type="info"
							@click="qqLogin"
							shape="circle"
							icon="qq-fill"
							size="normal"
							class="social-btn"
							:custom-style="qqBtnStyle"
						>
							QQ
						</up-button>
					</view>
					<!-- #endif -->
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-section">
				<up-text
					text="还没有账号？"
					color="#999"
					size="14"
				></up-text>
				<up-text
					text="立即注册"
					type="primary"
					@click="goRegister"
					size="14"
					class="register-link"
				></up-text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp模板',
			loginLoading: false,
			rememberPassword: false,
			passwordVisible: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: ['blur', 'change']
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: ['blur', 'change']
					},
					{
						validator: (rule, value, callback) => {
							// 验证用户名格式（字母、数字、下划线）或手机号格式
							const usernameReg = /^[a-zA-Z0-9_]{3,20}$/
							const phoneReg = /^1[3-9]\d{9}$/
							if (!usernameReg.test(value) && !phoneReg.test(value)) {
								callback(new Error('请输入有效的用户名或手机号'))
							} else {
								callback()
							}
						},
						trigger: ['blur', 'change']
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: ['blur', 'change']
					},
					{
						min: 6,
						max: 20,
						message: '密码长度应为6-20位',
						trigger: ['blur', 'change']
					},
					{
						validator: (rule, value, callback) => {
							// 验证密码强度（至少包含字母和数字）
							const passwordReg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/
							if (!passwordReg.test(value)) {
								callback(new Error('密码必须包含字母和数字'))
							} else {
								callback()
							}
						},
						trigger: ['blur', 'change']
					}
				]
			},

			// uView输入框样式
			inputStyle: {
				backgroundColor: '#f8f9fa',
				borderRadius: '12px',
				border: '2px solid #e9ecef',
				fontSize: '16px',
				padding: '0 16px'
			},

			wechatBtnStyle: {
				background: '#07c160',
				border: 'none'
			},
			qqBtnStyle: {
				background: '#12b7f5',
				border: 'none'
			}
		}
	},
	
	onLoad() {
		this.loadRememberedPassword()
	},
	
	methods: {
		// 处理登录
		async handleLogin() {
			// 防止重复提交
			if (this.loginLoading) {
				return
			}

			// 使用uView表单验证
			try {
				const valid = await this.$refs.loginForm.validate()
				if (!valid) {
					this.$u.toast('请检查表单信息')
					return
				}
			} catch (errors) {
				console.log('表单验证错误:', errors)
				// 显示第一个错误信息
				if (errors && errors.length > 0) {
					this.$u.toast(errors[0].message || '请检查表单信息')
				} else {
					this.$u.toast('请检查表单信息')
				}
				return
			}

			this.loginLoading = true

			try {
				// 模拟登录API调用
				console.log('登录信息:', {
					username: this.form.username,
					password: this.form.password
				})

				// 模拟网络延迟
				await new Promise(resolve => setTimeout(resolve, 1500))

				// 模拟登录验证（这里可以添加实际的API调用）
				const loginSuccess = await this.mockLoginAPI()

				if (loginSuccess) {
					this.$u.toast('登录成功')

					// 保存用户信息
					this.saveUserInfo({
						token: 'mock-token-' + Date.now(),
						userInfo: {
							username: this.form.username,
							avatar: '',
							phone: this.isPhoneNumber(this.form.username) ? this.form.username : ''
						}
					})

					// 记住密码
					if (this.rememberPassword) {
						this.savePassword()
					} else {
						this.clearPassword()
					}

					// 跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					this.$u.toast('用户名或密码错误')
				}
			} catch (error) {
				console.error('登录失败:', error)
				this.$u.toast('登录失败，请重试')
			} finally {
				this.loginLoading = false
			}
		},

		// 模拟登录API
		async mockLoginAPI() {
			// 模拟API验证逻辑
			const { username, password } = this.form

			// 这里可以添加实际的API调用
			// 目前模拟一些简单的验证逻辑
			if (username === 'admin' && password === 'admin123') {
				return true
			} else if (username === 'test' && password === 'test123') {
				return true
			} else if (this.isPhoneNumber(username) && password.length >= 6) {
				// 手机号登录的模拟验证
				return true
			}

			return false
		},

		// 判断是否为手机号
		isPhoneNumber(str) {
			const phoneReg = /^1[3-9]\d{9}$/
			return phoneReg.test(str)
		},
		
		// 微信登录
		wechatLogin() {
			this.$u.toast('微信登录功能待开发')
		},

		// QQ登录
		qqLogin() {
			this.$u.toast('QQ登录功能待开发')
		},

		// 切换密码可见性
		togglePasswordVisible() {
			this.passwordVisible = !this.passwordVisible
		},

		// 切换记住密码
		toggleRememberPassword() {
			this.rememberPassword = !this.rememberPassword
		},
		
		// 保存用户信息
		async saveUserInfo(userData) {
			// 保存到本地存储
			uni.setStorageSync('token', userData.token)
			uni.setStorageSync('userInfo', userData.userInfo)
			
			// 保存到Vuex
			this.$store.commit('user/setToken', userData.token)
			this.$store.commit('user/setUserInfo', userData.userInfo)
		},
		
		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', {
				username: this.form.username,
				password: this.form.password
			})
		},
		
		// 清除保存的密码
		clearPassword() {
			uni.removeStorageSync('rememberedPassword')
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('rememberedPassword')
			if (remembered) {
				this.form.username = remembered.username
				this.form.password = remembered.password
				this.rememberPassword = true
			}
		},
		
		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},
		
		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
}

.circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float 6s ease-in-out infinite;
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: 10%;
	right: 10%;
	animation-delay: 0s;
}

.circle-2 {
	width: 150rpx;
	height: 150rpx;
	bottom: 20%;
	left: 15%;
	animation-delay: 2s;
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	top: 60%;
	right: 20%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.7;
	}
	50% {
		transform: translateY(-20px) rotate(180deg);
		opacity: 1;
	}
}

.login-content {
	position: relative;
	z-index: 1;
	padding: 100rpx 60rpx 60rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-avatar {
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}

.app-name {
	margin-bottom: 20rpx;
	color: #fff !important;
}

.welcome-text {
	color: rgba(255, 255, 255, 0.8) !important;
}

.login-form {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
}

.form-item {
	margin-bottom: 30rpx;
}

/* 自定义输入框样式 */
.input-container {
	width: 100%;
	margin-bottom: 20rpx;
}

.input-label {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
	gap: 10rpx;
}

.label-text {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.uview-input {
	background: #f8f9fa !important;
	border-radius: 12rpx !important;
	border: 2rpx solid #e9ecef !important;
	transition: all 0.3s ease;
}

/* uView输入框内部样式覆盖 */
::v-deep .u-input__content {
	background: transparent !important;
	border: none !important;
	padding: 0 16rpx !important;
	min-height: 96rpx !important;
}

::v-deep .u-input__content__field-wrapper {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field {
	background: transparent !important;
	color: #333 !important;
	font-size: 32rpx !important;
	border: none !important;
	padding: 24rpx 0 !important;
}

::v-deep .u-input__content__field-wrapper__field::placeholder {
	color: #999 !important;
	font-size: 30rpx !important;
}

::v-deep .u-input__content__clear-icon,
::v-deep .u-input__content__password-icon {
	color: #ccc !important;
	margin-left: 16rpx !important;
}

/* 聚焦状态 */
::v-deep .u-input--focus .u-input__content {
	border-color: #667eea !important;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
	background: #fff !important;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx 0 40rpx;
}

/* 记住密码样式 */
.remember-password {
	cursor: pointer;
}

.checkbox-container {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.custom-checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ddd;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	background: #fff;
}

.custom-checkbox.checked {
	background: #667eea;
	border-color: #667eea;
}

.checkbox-label {
	font-size: 28rpx;
	color: #666;
	user-select: none;
}

.forgot-link {
	color: #667eea;
	font-size: 28rpx;
	cursor: pointer;
	text-decoration: none;
}

.forgot-link:hover {
	text-decoration: underline;
}

/* 登录按钮样式 */
.login-button-container {
	margin-top: 40rpx;
}

.login-button {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
	gap: 16rpx;
}

.login-button:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 12rpx 35rpx rgba(102, 126, 234, 0.4);
}

.login-button:active {
	transform: translateY(0);
}

.login-button.loading {
	opacity: 0.8;
	cursor: not-allowed;
}

.login-button-text {
	color: #fff;
	font-size: 32rpx;
	font-weight: 500;
}

.loading-icon {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.login-btn {
	margin-top: 20rpx;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.third-party-login {
	margin-top: 40rpx;
}

.third-party-buttons {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.third-btn {
	flex: 1;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.social-login-grid {
	display: flex;
	gap: 20rpx;
}

.social-btn {
	flex: 1;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.register-section {
	text-align: center;
	margin-top: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10rpx;
}

.register-link {
	cursor: pointer;
	text-decoration: underline;
}

/* uView组件样式覆盖 */
::v-deep .u-checkbox__label {
	font-size: 28rpx !important;
	color: #666 !important;
}

/* 表单验证错误信息样式 */
::v-deep .u-form-item__body__right__message {
	color: #f56c6c !important;
	font-size: 24rpx !important;
	margin-top: 8rpx !important;
	padding-left: 10rpx !important;
}

::v-deep .u-form-item--error .u-input__content {
	border-color: #f56c6c !important;
	background: #fef0f0 !important;
}

::v-deep .u-form-item--error .u-input__content__field-wrapper__field {
	color: #f56c6c !important;
}

/* 输入框内部样式覆盖 */
::v-deep .u-input__content {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
}

::v-deep .u-input__content__field-wrapper {
	background: transparent !important;
	border: none !important;
	padding: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field {
	background: transparent !important;
	color: #333 !important;
	font-size: 32rpx !important;
	border: none !important;
	padding: 12rpx 0 !important;
}

::v-deep .u-input__content__clear-icon {
	color: #999 !important;
}

::v-deep .u-input__content__password-icon {
	color: #999 !important;
}
</style>
