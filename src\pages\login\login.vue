<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<view class="login-content">
			<!-- 头部logo区域 -->
			<view class="login-header">
				<view class="logo-container">
					<image
						src="/static/logo.png"
						class="logo-image"
						mode="aspectFit"
					></image>
				</view>
				<view class="app-title">
					<text class="app-name">{{ appName }}</text>
					<text class="welcome-text">欢迎回来，请登录您的账户</text>
				</view>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<u-form
					:model="form"
					:rules="rules"
					ref="loginForm"
					label-position="top"
					:error-type="['message']"
				>
					<!-- 用户名输入框 -->
					<u-form-item prop="username" label="" class="form-item">
						<view class="input-container">
							<view class="input-label">
								<u-icon name="account-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">用户名/手机号</text>
							</view>
							<u-input
								v-model="form.username"
								placeholder="请输入用户名或手机号"
								border="none"
								clearable
								height="48"
								:custom-style="inputStyle"
								class="uview-input"
							></u-input>
						</view>
					</u-form-item>

					<!-- 密码输入框 -->
					<u-form-item prop="password" label="" class="form-item">
						<view class="input-container">
							<view class="input-label">
								<u-icon name="lock-fill" color="#667eea" size="18"></u-icon>
								<text class="label-text">密码</text>
							</view>
							<u-input
								v-model="form.password"
								type="password"
								placeholder="请输入密码"
								border="none"
								clearable
								password
								height="48"
								:custom-style="inputStyle"
								class="uview-input"
							></u-input>
						</view>
					</u-form-item>

					<!-- 登录选项 -->
					<view class="login-options">
						<view class="remember-password" @click="toggleRememberPassword">
							<view class="checkbox-container">
								<view class="custom-checkbox" :class="{ 'checked': rememberPassword }">
									<u-icon v-if="rememberPassword" name="checkmark" color="#fff" size="12"></u-icon>
								</view>
								<text class="checkbox-label">记住密码</text>
							</view>
						</view>
						<text class="forgot-link" @click="forgotPassword">忘记密码？</text>
					</view>

					<!-- 登录按钮 -->
					<view class="login-button-container">
						<view
							class="login-button"
							:class="{ 'loading': loginLoading }"
							@click="handleLogin"
						>
							<u-icon v-if="loginLoading" name="loading" color="#fff" size="18" class="loading-icon"></u-icon>
							<text class="login-button-text">{{ loginLoading ? '登录中...' : '登录' }}</text>
						</view>
					</view>
				</u-form>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<u-divider
					text="其他登录方式"
					text-color="#999"
					line-color="#e4e7ed"
					margin-top="40"
					margin-bottom="30"
				></u-divider>

				<view class="third-party-buttons">
					<!-- #ifdef MP-WEIXIN -->
					<u-button
						type="success"
						@click="wechatLogin"
						:custom-style="{ width: '100%', marginBottom: '20rpx' }"
					>
						微信登录
					</u-button>
					<!-- #endif -->

					<!-- #ifdef APP-PLUS -->
					<u-button
						type="info"
						@click="qqLogin"
						:custom-style="{ width: '100%', marginBottom: '20rpx' }"
					>
						QQ登录
					</u-button>
					<!-- #endif -->

					<!-- #ifdef H5 -->
					<view class="social-login-grid">
						<u-button
							type="success"
							@click="wechatLogin"
							:custom-style="{ width: '48%', marginRight: '4%' }"
						>
							微信
						</u-button>
						<u-button
							type="info"
							@click="qqLogin"
							:custom-style="{ width: '48%' }"
						>
							QQ
						</u-button>
					</view>
					<!-- #endif -->
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-section">
				<text class="register-text">还没有账号？</text>
				<text class="register-link" @click="goRegister">立即注册</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp Demo',
			loginLoading: false,
			rememberPassword: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: ['blur', 'change']
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: ['blur', 'change']
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: ['blur', 'change']
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: ['blur', 'change']
					}
				]
			},
			inputStyle: {
				backgroundColor: '#f8f9fa',
				borderRadius: '12rpx',
				padding: '0 20rpx'
			}
		}
	},
	methods: {
		// 切换记住密码
		toggleRememberPassword() {
			this.rememberPassword = !this.rememberPassword
		},

		// 处理登录
		async handleLogin() {
			// 表单验证
			const valid = await this.$refs.loginForm.validate()
			if (!valid) {
				this.$u.toast('请检查表单信息')
				return
			}

			this.loginLoading = true
			
			try {
				// 模拟登录API调用
				await this.mockLoginAPI()
				
				// 登录成功处理
				this.$u.toast('登录成功')
				
				// 保存用户信息
				if (this.rememberPassword) {
					this.savePassword()
				}
				
				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1500)
				
			} catch (error) {
				this.$u.toast(error.message || '登录失败')
			} finally {
				this.loginLoading = false
			}
		},

		// 模拟登录API
		mockLoginAPI() {
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					if (this.form.username === 'admin' && this.form.password === '123456') {
						resolve({
							token: 'mock-token-123456',
							userInfo: {
								id: 1,
								username: this.form.username,
								nickname: '管理员'
							}
						})
					} else {
						reject(new Error('用户名或密码错误'))
					}
				}, 2000)
			})
		},

		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', this.form.password)
		},

		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},

		// 微信登录
		wechatLogin() {
			this.$u.toast('微信登录功能待开发')
		},

		// QQ登录
		qqLogin() {
			this.$u.toast('QQ登录功能待开发')
		},

		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
}

.circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);

	&.circle-1 {
		width: 200rpx;
		height: 200rpx;
		top: 10%;
		right: -50rpx;
		animation: float 6s ease-in-out infinite;
	}

	&.circle-2 {
		width: 150rpx;
		height: 150rpx;
		top: 60%;
		left: -30rpx;
		animation: float 8s ease-in-out infinite reverse;
	}

	&.circle-3 {
		width: 100rpx;
		height: 100rpx;
		top: 30%;
		left: 20%;
		animation: float 10s ease-in-out infinite;
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20px); }
}

.login-content {
	position: relative;
	z-index: 2;
	padding: 80rpx 60rpx 60rpx;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.login-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-container {
	margin-bottom: 40rpx;
}

.logo-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
}

.app-title {
	.app-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 20rpx;
	}

	.welcome-text {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
}

.login-form {
	background: #fff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.form-item {
	margin-bottom: 40rpx;
}

.input-container {
	.input-label {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.label-text {
			margin-left: 16rpx;
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40rpx 0;
}

.remember-password {
	.checkbox-container {
		display: flex;
		align-items: center;

		.custom-checkbox {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #ddd;
			border-radius: 6rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			transition: all 0.3s;

			&.checked {
				background: #667eea;
				border-color: #667eea;
			}
		}

		.checkbox-label {
			font-size: 28rpx;
			color: #666;
		}
	}
}

.forgot-link {
	font-size: 28rpx;
	color: #667eea;
}

.login-button-container {
	margin-top: 60rpx;
}

.login-button {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;

	&.loading {
		opacity: 0.8;
	}

	.loading-icon {
		margin-right: 16rpx;
		animation: spin 1s linear infinite;
	}

	.login-button-text {
		font-size: 32rpx;
		color: #fff;
		font-weight: 500;
	}
}

@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.third-party-login {
	margin-bottom: 40rpx;
}

.social-login-grid {
	display: flex;
	justify-content: space-between;
}

.register-section {
	text-align: center;

	.register-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.register-link {
		margin-left: 10rpx;
		font-size: 28rpx;
		color: #fff;
		text-decoration: underline;
	}
}

/* uView输入框样式覆盖 */
::v-deep .u-input__content {
	background-color: #f8f9fa !important;
	border-radius: 12rpx !important;
	padding: 0 20rpx !important;
}

::v-deep .u-input--focus .u-input__content {
	border-color: #667eea !important;
}
</style>
