<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<view class="login-content">
			<!-- 头部logo区域 -->
			<view class="login-header">
				<u-avatar
					:src="'/static/logo.png'"
					size="120"
					shape="circle"
					class="logo-avatar"
				></u-avatar>
				<u-text
					:text="appName"
					type="primary"
					size="24"
					bold
					class="app-name"
				></u-text>
				<u-text
					text="欢迎回来，请登录您的账户"
					type="tips"
					size="14"
					class="welcome-text"
				></u-text>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<u-form
					:model="form"
					:rules="rules"
					ref="loginForm"
					label-position="top"
				>
					<u-form-item
						label="用户名/手机号"
						prop="username"
						class="form-item"
					>
						<u-input
							v-model="form.username"
							placeholder="请输入用户名或手机号"
							prefix-icon="account"
							border="surround"
							clearable
						></u-input>
					</u-form-item>

					<u-form-item
						label="密码"
						prop="password"
						class="form-item"
					>
						<u-input
							v-model="form.password"
							type="password"
							placeholder="请输入密码"
							prefix-icon="lock"
							border="surround"
							clearable
						></u-input>
					</u-form-item>

					<view class="login-options">
						<u-checkbox
							v-model="rememberPassword"
							active-color="#5677fc"
							size="18"
						>记住密码</u-checkbox>

						<u-text
							text="忘记密码？"
							type="primary"
							@click="forgotPassword"
							class="forgot-link"
						></u-text>
					</view>

					<u-button
						type="primary"
						:loading="loginLoading"
						@click="handleLogin"
						shape="circle"
						size="large"
						class="login-btn"
						:custom-style="loginBtnStyle"
					>
						{{ loginLoading ? '登录中...' : '登录' }}
					</u-button>
				</u-form>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<u-divider
					text="其他登录方式"
					text-color="#999"
					line-color="#e4e7ed"
					margin-top="40"
					margin-bottom="30"
				></u-divider>

				<view class="third-party-buttons">
					<!-- #ifdef MP-WEIXIN -->
					<u-button
						type="success"
						@click="wechatLogin"
						shape="circle"
						icon="weixin-fill"
						size="normal"
						class="third-btn"
						:custom-style="wechatBtnStyle"
					>
						微信登录
					</u-button>
					<!-- #endif -->

					<!-- #ifdef APP-PLUS -->
					<u-button
						type="info"
						@click="qqLogin"
						shape="circle"
						icon="qq-fill"
						size="normal"
						class="third-btn"
						:custom-style="qqBtnStyle"
					>
						QQ登录
					</u-button>
					<!-- #endif -->

					<!-- H5端显示更多登录方式 -->
					<!-- #ifdef H5 -->
					<view class="social-login-grid">
						<u-button
							type="success"
							@click="wechatLogin"
							shape="circle"
							icon="weixin-fill"
							size="normal"
							class="social-btn"
							:custom-style="wechatBtnStyle"
						>
							微信
						</u-button>

						<u-button
							type="info"
							@click="qqLogin"
							shape="circle"
							icon="qq-fill"
							size="normal"
							class="social-btn"
							:custom-style="qqBtnStyle"
						>
							QQ
						</u-button>
					</view>
					<!-- #endif -->
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-section">
				<u-text
					text="还没有账号？"
					color="#999"
					size="14"
				></u-text>
				<u-text
					text="立即注册"
					type="primary"
					@click="goRegister"
					size="14"
					class="register-link"
				></u-text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			appName: 'UniApp模板',
			loginLoading: false,
			rememberPassword: false,
			form: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名或手机号',
						trigger: 'blur'
					},
					{
						min: 3,
						message: '用户名长度不能少于3位',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: 'blur'
					}
				]
			},
			// uView按钮样式
			loginBtnStyle: {
				background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				border: 'none',
				height: '50px',
				fontSize: '16px'
			},
			wechatBtnStyle: {
				background: '#07c160',
				border: 'none'
			},
			qqBtnStyle: {
				background: '#12b7f5',
				border: 'none'
			}
		}
	},
	
	onLoad() {
		this.loadRememberedPassword()
	},
	
	methods: {
		// 处理登录
		async handleLogin() {
			// 使用uView表单验证
			try {
				await this.$refs.loginForm.validate()
			} catch (errors) {
				this.$u.toast('请检查表单信息')
				return
			}

			this.loginLoading = true

			try {
				// 模拟登录API调用
				console.log('登录信息:', {
					username: this.form.username,
					password: this.form.password
				})

				// 模拟网络延迟
				await new Promise(resolve => setTimeout(resolve, 1000))

				// 模拟登录成功
				this.$u.toast('登录成功')

				// 保存用户信息
				this.saveUserInfo({
					token: 'mock-token-' + Date.now(),
					userInfo: {
						username: this.form.username,
						avatar: '',
						phone: ''
					}
				})

				// 记住密码
				if (this.rememberPassword) {
					this.savePassword()
				} else {
					this.clearPassword()
				}

				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1500)
			} catch (error) {
				console.error('登录失败:', error)
				this.$u.toast('登录失败，请重试')
			} finally {
				this.loginLoading = false
			}
		},
		
		// 微信登录
		wechatLogin() {
			this.$u.toast('微信登录功能待开发')
		},

		// QQ登录
		qqLogin() {
			this.$u.toast('QQ登录功能待开发')
		},
		
		// 保存用户信息
		async saveUserInfo(userData) {
			// 保存到本地存储
			uni.setStorageSync('token', userData.token)
			uni.setStorageSync('userInfo', userData.userInfo)
			
			// 保存到Vuex
			this.$store.commit('user/setToken', userData.token)
			this.$store.commit('user/setUserInfo', userData.userInfo)
		},
		
		// 保存密码
		savePassword() {
			uni.setStorageSync('rememberedPassword', {
				username: this.form.username,
				password: this.form.password
			})
		},
		
		// 清除保存的密码
		clearPassword() {
			uni.removeStorageSync('rememberedPassword')
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('rememberedPassword')
			if (remembered) {
				this.form.username = remembered.username
				this.form.password = remembered.password
				this.rememberPassword = true
			}
		},
		
		// 忘记密码
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		},
		
		// 去注册
		goRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 100rpx 60rpx 60rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 100rpx;
}

.logo {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
}

.login-form {
	background: #fff;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
	margin-bottom: 40rpx;
}

.input {
	width: 100%;
	height: 88rpx;
	padding: 0 20rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 10rpx;
	font-size: 32rpx;
	box-sizing: border-box;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40rpx 0;
}

.forgot-password {
	color: #3c9cff;
	font-size: 28rpx;
}

.login-btn {
	width: 100%;
	margin-top: 40rpx;
	height: 88rpx;
	border-radius: 44rpx;
	background: #3c9cff;
	color: #fff;
	font-size: 32rpx;
	border: none;
}

.third-party-login {
	margin-top: 60rpx;
}

.divider {
	text-align: center;
	position: relative;
	margin-bottom: 40rpx;
}

.divider text {
	background: transparent;
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
	padding: 0 20rpx;
}

.divider::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	height: 1rpx;
	background: rgba(255, 255, 255, 0.3);
	z-index: -1;
}

.third-party-buttons {
	display: flex;
	gap: 20rpx;
}

.third-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.wechat-btn {
	background: #07c160;
	color: #fff;
}

.qq-btn {
	background: #12b7f5;
	color: #fff;
}

.register-link {
	text-align: center;
	margin-top: 60rpx;
}

.register-link text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
}

.link-text {
	color: #fff;
	font-weight: bold;
	margin-left: 10rpx;
}
</style>
