/**
 * 工具函数集合
 */

/**
 * 格式化时间
 * @param {Date|String|Number} date 时间
 * @param {String} format 格式 默认 'YYYY-MM-DD HH:mm:ss'
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
	if (!date) return ''
	
	const d = new Date(date)
	if (isNaN(d.getTime())) return ''
	
	const year = d.getFullYear()
	const month = String(d.getMonth() + 1).padStart(2, '0')
	const day = String(d.getDate()).padStart(2, '0')
	const hour = String(d.getHours()).padStart(2, '0')
	const minute = String(d.getMinutes()).padStart(2, '0')
	const second = String(d.getSeconds()).padStart(2, '0')
	
	return format
		.replace('YYYY', year)
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hour)
		.replace('mm', minute)
		.replace('ss', second)
}

/**
 * 相对时间
 * @param {Date|String|Number} date 时间
 */
export function timeAgo(date) {
	if (!date) return ''
	
	const now = new Date()
	const target = new Date(date)
	const diff = now - target
	
	const minute = 60 * 1000
	const hour = 60 * minute
	const day = 24 * hour
	const month = 30 * day
	const year = 365 * day
	
	if (diff < minute) {
		return '刚刚'
	} else if (diff < hour) {
		return Math.floor(diff / minute) + '分钟前'
	} else if (diff < day) {
		return Math.floor(diff / hour) + '小时前'
	} else if (diff < month) {
		return Math.floor(diff / day) + '天前'
	} else if (diff < year) {
		return Math.floor(diff / month) + '个月前'
	} else {
		return Math.floor(diff / year) + '年前'
	}
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} delay 延迟时间
 */
export function debounce(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (timer) clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(this, args)
		}, delay)
	}
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} delay 延迟时间
 */
export function throttle(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (!timer) {
			timer = setTimeout(() => {
				func.apply(this, args)
				timer = null
			}, delay)
		}
	}
}

/**
 * 深拷贝
 * @param {*} obj 要拷贝的对象
 */
export function deepClone(obj) {
	if (obj === null || typeof obj !== 'object') return obj
	if (obj instanceof Date) return new Date(obj)
	if (obj instanceof Array) return obj.map(item => deepClone(item))
	if (typeof obj === 'object') {
		const copy = {}
		Object.keys(obj).forEach(key => {
			copy[key] = deepClone(obj[key])
		})
		return copy
	}
}

/**
 * 生成唯一ID
 */
export function generateId() {
	return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 手机号验证
 * @param {String} phone 手机号
 */
export function validatePhone(phone) {
	return /^1[3-9]\d{9}$/.test(phone)
}

/**
 * 邮箱验证
 * @param {String} email 邮箱
 */
export function validateEmail(email) {
	return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

/**
 * 身份证验证
 * @param {String} idCard 身份证号
 */
export function validateIdCard(idCard) {
	return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
}

/**
 * 密码强度验证
 * @param {String} password 密码
 */
export function validatePassword(password) {
	// 至少包含数字和字母，长度6-20位
	return /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/.test(password)
}

/**
 * 格式化文件大小
 * @param {Number} size 文件大小（字节）
 */
export function formatFileSize(size) {
	if (!size) return '0 B'
	
	const units = ['B', 'KB', 'MB', 'GB', 'TB']
	let index = 0
	
	while (size >= 1024 && index < units.length - 1) {
		size /= 1024
		index++
	}
	
	return Math.round(size * 100) / 100 + ' ' + units[index]
}

/**
 * 获取文件扩展名
 * @param {String} filename 文件名
 */
export function getFileExtension(filename) {
	return filename.split('.').pop().toLowerCase()
}

/**
 * 数组去重
 * @param {Array} arr 数组
 * @param {String} key 去重的键名（对象数组）
 */
export function uniqueArray(arr, key) {
	if (!Array.isArray(arr)) return []
	
	if (key) {
		const seen = new Set()
		return arr.filter(item => {
			const value = item[key]
			if (seen.has(value)) {
				return false
			}
			seen.add(value)
			return true
		})
	} else {
		return [...new Set(arr)]
	}
}

/**
 * 数组分组
 * @param {Array} arr 数组
 * @param {String|Function} key 分组的键名或函数
 */
export function groupBy(arr, key) {
	if (!Array.isArray(arr)) return {}
	
	return arr.reduce((groups, item) => {
		const group = typeof key === 'function' ? key(item) : item[key]
		if (!groups[group]) {
			groups[group] = []
		}
		groups[group].push(item)
		return groups
	}, {})
}

/**
 * 获取URL参数
 * @param {String} name 参数名
 * @param {String} url URL地址
 */
export function getUrlParam(name, url = window.location.href) {
	const regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i')
	const match = regex.exec(url)
	return match ? decodeURIComponent(match[1]) : null
}

/**
 * 设置页面标题
 * @param {String} title 标题
 */
export function setPageTitle(title) {
	uni.setNavigationBarTitle({
		title
	})
}

/**
 * 显示消息提示
 * @param {String} title 提示内容
 * @param {String} icon 图标类型
 * @param {Number} duration 显示时长
 */
export function showToast(title, icon = 'none', duration = 2000) {
	uni.showToast({
		title,
		icon,
		duration
	})
}

/**
 * 显示确认对话框
 * @param {String} content 内容
 * @param {String} title 标题
 */
export function showConfirm(content, title = '提示') {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title,
			content,
			success: (res) => {
				if (res.confirm) {
					resolve(true)
				} else {
					reject(false)
				}
			},
			fail: () => {
				reject(false)
			}
		})
	})
}

/**
 * 复制到剪贴板
 * @param {String} text 要复制的文本
 */
export function copyToClipboard(text) {
	return new Promise((resolve, reject) => {
		uni.setClipboardData({
			data: text,
			success: () => {
				showToast('复制成功')
				resolve(true)
			},
			fail: () => {
				showToast('复制失败')
				reject(false)
			}
		})
	})
}

/**
 * 获取系统信息
 */
export function getSystemInfo() {
	return new Promise((resolve) => {
		uni.getSystemInfo({
			success: (res) => {
				resolve(res)
			},
			fail: () => {
				resolve({})
			}
		})
	})
}

// 默认导出所有工具函数
export default {
	formatDate,
	timeAgo,
	debounce,
	throttle,
	deepClone,
	generateId,
	validatePhone,
	validateEmail,
	validateIdCard,
	validatePassword,
	formatFileSize,
	getFileExtension,
	uniqueArray,
	groupBy,
	getUrlParam,
	setPageTitle,
	showToast,
	showConfirm,
	copyToClipboard,
	getSystemInfo
}
