<template>
	<view class="content">
		<view class="header">
			<image class="logo" src="/static/logo.png"></image>
			<text class="app-title">欢迎使用UniApp</text>
		</view>

		<view class="user-info" v-if="userInfo && userInfo.username">
			<text class="welcome-text">欢迎回来，{{ userInfo.username }}！</text>
		</view>

		<view class="quick-actions">
			<view class="action-item" @click="goToUser">
				<text class="action-icon">👤</text>
				<text class="action-text">个人中心</text>
			</view>

			<view class="action-item" @click="logout">
				<text class="action-icon">🚪</text>
				<text class="action-text">退出登录</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'

	export default {
		data() {
			return {
				title: 'Hello'
			}
		},

		computed: {
			...mapState('user', ['token', 'userInfo', 'isLogin'])
		},

		onLoad() {
			this.checkLoginStatus()
		},

		onShow() {
			// 每次显示页面时都检查登录状态
			this.checkLoginStatus()
		},

		methods: {
			// 检查登录状态
			checkLoginStatus() {
				// 检查本地存储中的token
				const token = uni.getStorageSync('token')
				const userInfo = uni.getStorageSync('userInfo')

				if (!token || !userInfo) {
					// 未登录，跳转到登录页
					uni.reLaunch({
						url: '/pages/login/login'
					})
					return
				}

				// 已登录，更新Vuex状态
				this.$store.commit('user/setToken', token)
				this.$store.commit('user/setUserInfo', userInfo)
				this.$store.commit('user/setLoginStatus', true)

				// 可以在这里添加验证token有效性的逻辑
				this.validateToken(token)
			},

			// 验证token有效性（可选）
			async validateToken(token) {
				try {
					// 这里可以调用后端接口验证token
					// const response = await this.$http.post('/api/validate-token', { token })
					// if (!response.data.valid) {
					//     this.logout()
					// }
					console.log('验证token:', token)
				} catch (error) {
					console.error('Token验证失败:', error)
					// 如果验证失败，可以选择退出登录
					// this.logout()
				}
			},

			// 去个人中心
			goToUser() {
				uni.navigateTo({
					url: '/pages/user/user'
				})
			},

			// 退出登录
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.removeStorageSync('token')
							uni.removeStorageSync('userInfo')
							this.$store.commit('user/clearUserInfo')

							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							})

							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/login/login'
								})
							}, 1500)
						}
					}
				})
			}
		}
	}
</script>

<style scoped>
	.content {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 60rpx 40rpx;
	}

	.header {
		text-align: center;
		margin-bottom: 80rpx;
	}

	.logo {
		height: 160rpx;
		width: 160rpx;
		margin-bottom: 30rpx;
	}

	.app-title {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: white;
	}

	.user-info {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 60rpx;
		text-align: center;
	}

	.welcome-text {
		font-size: 32rpx;
		color: white;
		font-weight: 500;
	}

	.quick-actions {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.action-item {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 40rpx;
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.action-item:hover {
		background: white;
		transform: translateY(-2rpx);
	}

	.action-icon {
		font-size: 48rpx;
		margin-right: 30rpx;
	}

	.action-text {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
	}
</style>
