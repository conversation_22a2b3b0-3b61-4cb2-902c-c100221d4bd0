<template>
	<view class="user-container">
		<view class="user-header">
			<view class="user-info" v-if="userInfo">
				<image class="avatar" :src="userInfo.avatar || '/static/logo.png'" mode="aspectFill"></image>
				<view class="info-text">
					<text class="username">{{ userInfo.username }}</text>
					<text class="phone">{{ userInfo.phone }}</text>
				</view>
			</view>
			<view class="login-prompt" v-else @click="goLogin">
				<image class="avatar" src="/static/logo.png" mode="aspectFill"></image>
				<text class="login-text">点击登录</text>
			</view>
		</view>
		
		<view class="user-menu">
			<view class="menu-group">
				<view class="menu-item" @click="goProfile">
					<text class="menu-icon">👤</text>
					<text class="menu-title">个人资料</text>
					<text class="menu-arrow">></text>
				</view>

				<view class="menu-item" @click="goChangePassword">
					<text class="menu-icon">🔒</text>
					<text class="menu-title">修改密码</text>
					<text class="menu-arrow">></text>
				</view>

				<view class="menu-item" @click="goNotification">
					<text class="menu-icon">🔔</text>
					<text class="menu-title">消息通知</text>
					<text class="menu-arrow">></text>
				</view>

				<view class="menu-item" @click="goPrivacySetting">
					<text class="menu-icon">⚙️</text>
					<text class="menu-title">隐私设置</text>
					<text class="menu-arrow">></text>
				</view>

				<view class="menu-item" @click="goAbout">
					<text class="menu-icon">ℹ️</text>
					<text class="menu-title">关于我们</text>
					<text class="menu-arrow">></text>
				</view>

				<view class="menu-item" @click="goFeedback">
					<text class="menu-icon">💬</text>
					<text class="menu-title">意见反馈</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
		</view>
		
		<view class="logout-section" v-if="userInfo">
			<button
				type="warn"
				@click="handleLogout"
				class="logout-btn"
			>
				退出登录
			</button>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex'

export default {
	computed: {
		...mapState('user', ['userInfo', 'token'])
	},
	
	onShow() {
		// 页面显示时检查登录状态
		this.checkLoginStatus()
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token')
			const userInfo = uni.getStorageSync('userInfo')
			
			if (token && userInfo) {
				this.$store.commit('user/setToken', token)
				this.$store.commit('user/setUserInfo', userInfo)
			}
		},
		
		// 去登录
		goLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		},
		
		// 去个人资料
		goProfile() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/profile/profile'
			})
		},
		
		// 去修改密码
		goChangePassword() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/change-password/change-password'
			})
		},
		
		// 去消息通知
		goNotification() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/notification/notification'
			})
		},
		
		// 去隐私设置
		goPrivacySetting() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/privacy-setting/privacy-setting'
			})
		},
		
		// 去关于我们
		goAbout() {
			uni.navigateTo({
				url: '/pages/about/about'
			})
		},
		
		// 去意见反馈
		goFeedback() {
			if (!this.checkAuth()) return
			
			uni.navigateTo({
				url: '/pages/feedback/feedback'
			})
		},
		
		// 检查认证状态
		checkAuth() {
			if (!this.token || !this.userInfo) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					this.goLogin()
				}, 1500)
				return false
			}
			return true
		},
		
		// 处理退出登录
		handleLogout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.logout()
					}
				}
			})
		},
		
		// 退出登录
		async logout() {
			try {
				// 调用退出登录接口
				await this.$http.post('/api/logout')
			} catch (error) {
				console.error('退出登录失败:', error)
			} finally {
				// 清除本地存储
				uni.removeStorageSync('token')
				uni.removeStorageSync('userInfo')
				
				// 清除Vuex状态
				this.$store.commit('user/setToken', '')
				this.$store.commit('user/setUserInfo', null)

				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})
				
				// 跳转到登录页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/login/login'
					})
				}, 1500)
			}
		}
	}
}
</script>

<style scoped>
.user-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.user-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.user-info {
	display: flex;
	align-items: center;
}

.login-prompt {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
	background-color: rgba(255, 255, 255, 0.2);
}

.info-text {
	flex: 1;
}

.username {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.phone {
	display: block;
	font-size: 28rpx;
	opacity: 0.8;
}

.login-text {
	font-size: 32rpx;
	opacity: 0.9;
}

.user-menu {
	margin: 20rpx;
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.menu-group {
	background: white;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-item:hover {
	background-color: #f8f8f8;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.menu-title {
	flex: 1;
	font-size: 32rpx;
	color: #333;
}

.menu-arrow {
	font-size: 28rpx;
	color: #999;
}

.logout-section {
	margin: 40rpx 20rpx;
}

.logout-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}
</style>
