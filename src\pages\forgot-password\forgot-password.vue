<template>
	<view class="forgot-container">
		<view class="forgot-header">
			<text class="title">忘记密码</text>
			<text class="subtitle">通过手机号重置您的密码</text>
		</view>
		
		<view class="forgot-form">
			<u-form :model="form" ref="forgotForm" :rules="rules">
				<u-form-item prop="phone" borderBottom>
					<u-input 
						v-model="form.phone" 
						placeholder="请输入手机号" 
						prefixIcon="phone"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="code" borderBottom>
					<view class="code-input">
						<u-input 
							v-model="form.code" 
							placeholder="请输入验证码" 
							prefixIcon="checkmark"
							clearable
						/>
						<u-button 
							:disabled="codeDisabled" 
							@click="sendCode"
							size="mini"
							type="primary"
							class="code-btn"
						>
							{{ codeText }}
						</u-button>
					</view>
				</u-form-item>
				
				<u-form-item prop="newPassword" borderBottom>
					<u-input 
						v-model="form.newPassword" 
						type="password" 
						placeholder="请输入新密码" 
						prefixIcon="lock"
						:passwordIcon="true"
						clearable
					/>
				</u-form-item>
				
				<u-form-item prop="confirmPassword" borderBottom>
					<u-input 
						v-model="form.confirmPassword" 
						type="password" 
						placeholder="请确认新密码" 
						prefixIcon="lock"
						:passwordIcon="true"
						clearable
					/>
				</u-form-item>
				
				<u-button 
					type="primary" 
					:loading="resetLoading"
					@click="handleReset"
					class="reset-btn"
				>
					重置密码
				</u-button>
			</u-form>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			resetLoading: false,
			codeDisabled: false,
			codeText: '获取验证码',
			countdown: 60,
			form: {
				phone: '',
				code: '',
				newPassword: '',
				confirmPassword: ''
			},
			rules: {
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: 'blur'
					},
					{
						pattern: /^1[3-9]\d{9}$/,
						message: '请输入正确的手机号',
						trigger: 'blur'
					}
				],
				code: [
					{
						required: true,
						message: '请输入验证码',
						trigger: 'blur'
					},
					{
						len: 6,
						message: '验证码为6位数字',
						trigger: 'blur'
					}
				],
				newPassword: [
					{
						required: true,
						message: '请输入新密码',
						trigger: 'blur'
					},
					{
						min: 6,
						max: 20,
						message: '密码长度为6-20位',
						trigger: 'blur'
					}
				],
				confirmPassword: [
					{
						required: true,
						message: '请确认新密码',
						trigger: 'blur'
					},
					{
						validator: (rule, value, callback) => {
							if (value !== this.form.newPassword) {
								callback(new Error('两次输入的密码不一致'))
							} else {
								callback()
							}
						},
						trigger: 'blur'
					}
				]
			}
		}
	},
	
	methods: {
		// 发送验证码
		async sendCode() {
			if (!this.form.phone) {
				this.$u.toast('请输入手机号')
				return
			}
			
			if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
				this.$u.toast('请输入正确的手机号')
				return
			}
			
			try {
				const result = await this.$http.post('/api/send-code', {
					phone: this.form.phone,
					type: 'reset'
				})
				
				if (result.code === 200) {
					this.$u.toast('验证码已发送')
					this.startCountdown()
				} else {
					this.$u.toast(result.message || '发送失败')
				}
			} catch (error) {
				console.error('发送验证码失败:', error)
				this.$u.toast('发送失败，请重试')
			}
		},
		
		// 开始倒计时
		startCountdown() {
			this.codeDisabled = true
			this.countdown = 60
			
			const timer = setInterval(() => {
				this.countdown--
				this.codeText = `${this.countdown}s后重发`
				
				if (this.countdown <= 0) {
					clearInterval(timer)
					this.codeDisabled = false
					this.codeText = '获取验证码'
				}
			}, 1000)
		},
		
		// 处理重置密码
		async handleReset() {
			const valid = await this.$refs.forgotForm.validate()
			if (!valid) return
			
			this.resetLoading = true
			
			try {
				const resetData = {
					phone: this.form.phone,
					code: this.form.code,
					newPassword: this.form.newPassword
				}
				
				const result = await this.$http.post('/api/reset-password', resetData)
				
				if (result.code === 200) {
					this.$u.toast('密码重置成功')
					
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					this.$u.toast(result.message || '重置失败')
				}
			} catch (error) {
				console.error('重置密码失败:', error)
				this.$u.toast('重置失败，请重试')
			} finally {
				this.resetLoading = false
			}
		}
	}
}
</script>
