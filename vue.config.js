const path = require('path')

module.exports = {
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  css: {
    loaderOptions: {
      sass: {
        // 使用现代的 sass (Dart Sass) 而不是 node-sass
        implementation: require('sass'),
        sassOptions: {
          outputStyle: 'expanded'
        },
        // 全局引入uView Plus的样式变量和mixin
        additionalData: `@import "uview-plus/theme.scss";`
      },
      scss: {
        // 使用现代的 sass (Dart Sass) 而不是 node-sass
        implementation: require('sass'),
        sassOptions: {
          outputStyle: 'expanded'
        },
        // 全局引入uView Plus的样式变量和mixin
        additionalData: `@import "uview-plus/theme.scss";`
      }
    }
  }
}
