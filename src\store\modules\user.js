const state = {
	token: uni.getStorageSync('token') || '',
	userInfo: uni.getStorageSync('userInfo') || {},
	isLogin: false
}

const mutations = {
	// 设置token
	setToken(state, token) {
		state.token = token
		state.isLogin = !!token
		uni.setStorageSync('token', token)
	},
	
	// 设置用户信息
	setUserInfo(state, userInfo) {
		state.userInfo = userInfo
		uni.setStorageSync('userInfo', userInfo)
	},
	
	// 清除用户信息
	clearUserInfo(state) {
		state.token = ''
		state.userInfo = {}
		state.isLogin = false
		uni.removeStorageSync('token')
		uni.removeStorageSync('userInfo')
	},
	
	// 设置登录状态
	setLoginStatus(state, status) {
		state.isLogin = status
	}
}

const actions = {
	// 登录
	async login({ commit }, loginData) {
		try {
			// 这里应该调用登录接口
			const response = await uni.request({
				url: '/api/login',
				method: 'POST',
				data: loginData
			})
			
			if (response.data.code === 200) {
				const { token, userInfo } = response.data.data
				commit('setToken', token)
				commit('setUserInfo', userInfo)
				commit('setLoginStatus', true)
				return Promise.resolve(response.data)
			} else {
				return Promise.reject(response.data)
			}
		} catch (error) {
			return Promise.reject(error)
		}
	},
	
	// 退出登录
	logout({ commit }) {
		commit('clearUserInfo')
		// 跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})
	},
	
	// 检查登录状态
	checkLoginStatus({ commit, state }) {
		const token = uni.getStorageSync('token')
		const userInfo = uni.getStorageSync('userInfo')
		
		if (token && userInfo) {
			commit('setToken', token)
			commit('setUserInfo', userInfo)
			commit('setLoginStatus', true)
		} else {
			commit('setLoginStatus', false)
		}
	}
}

const getters = {
	// 获取用户信息
	getUserInfo: state => state.userInfo,
	
	// 获取token
	getToken: state => state.token,
	
	// 获取登录状态
	getLoginStatus: state => state.isLogin,
	
	// 获取用户头像
	getUserAvatar: state => state.userInfo.avatar || '/static/images/default-avatar.png',
	
	// 获取用户昵称
	getUserNickname: state => state.userInfo.nickname || '未设置昵称'
}

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters
}
