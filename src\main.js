import Vue from 'vue'
import App from './App'
import store from './store'
import http from './utils/request'
import utils from './utils'
import './uni.promisify.adaptor'

// 引入uView UI
import uView from 'uview-ui'
Vue.use(uView)

Vue.config.productionTip = false

// 挂载到全局
Vue.prototype.$http = http
Vue.prototype.$utils = utils

App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})
app.$mount()
